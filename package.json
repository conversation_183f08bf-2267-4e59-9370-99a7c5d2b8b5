{"$schema": "https://json.schemastore.org/package.json", "name": "cashback-deals", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "clean": "rm -rf .next", "clean:build": "npm run clean && npm run build", "start": "next start", "lint": "next lint", "seo-audit": "lighthouse --only=seo --output=html --output-path=./reports/seo-audit.html", "perf-test": "lighthouse --only=performance --output=html --output-path=./reports/performance.html", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:camelcase": "jest --testPathPatterns=camelcase", "test:transformations": "jest --testPathPatterns=transformations", "test:api": "jest --testPathPatterns=api", "test:metadata": "jest --testPathPatterns=metadata", "validate:schema": "node scripts/validate-structured-data.js", "audit:performance": "lighthouse --only=performance --output=json --quiet", "audit:seo": "lighthouse --only=seo --output=json --quiet", "generate:sitemap": "node scripts/generate-sitemap.js", "optimize:images": "next-optimized-images", "build:analyze": "ANALYZE=true npm run build", "preview": "npm run build && npm run start", "seo:test": "node scripts/seo-test.js", "seo:monitor": "curl -s http://localhost:3000/api/seo/monitor", "seo:validate-sitemap": "node -e \"console.log('Sitemap validation would run here')\"", "performance:check": "curl -s http://localhost:3000/api/analytics/web-vitals"}, "dependencies": {"@hookform/resolvers": "^5.1.1", "@marsidev/react-turnstile": "^1.1.0", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-navigation-menu": "^1.2.13", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-slot": "^1.2.3", "@sentry/nextjs": "^9.36.0", "@shadcn/ui": "^0.0.4", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.50.4", "@tanstack/react-query-devtools": "^5.82.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "critters": "^0.0.25", "dotenv": "^17.2.0", "framer-motion": "^12.23.1", "graphql": "^16.11.0", "isomorphic-dompurify": "^2.26.0", "lucide-react": "^0.525.0", "next": "15.3.5", "next-auth": "^4.24.11", "next-i18next": "^15.4.2", "next-seo": "^6.8.0", "nodemailer": "6.10.1", "papaparse": "^5.5.3", "path-to-regexp": "8.2.0", "react": "19.1.0", "react-dom": "19.1.0", "react-hook-form": "^7.60.0", "react-hot-toast": "^2.5.2", "react-loading-skeleton": "^3.5.0", "tailwind-merge": "^3.3.1", "tailwindcss-animate": "^1.0.7", "web-vitals": "^5.0.3", "zod": "^4.0.0"}, "devDependencies": {"@babel/plugin-transform-modules-commonjs": "^7.27.1", "@babel/plugin-transform-runtime": "^7.28.0", "@babel/preset-env": "^7.28.0", "@babel/preset-react": "^7.27.1", "@babel/preset-typescript": "^7.27.1", "@cloudflare/next-on-pages": "^1.13.12", "@edge-runtime/primitives": "^6.0.0", "@playwright/test": "^1.53.2", "@swc/jest": "^0.2.39", "@tailwindcss/postcss": "^4.1.11", "@tanstack/react-query": "^5.82.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@types/dompurify": "^3.2.0", "@types/jest": "^30.0.0", "@types/node": "^24.0.12", "@types/nodemailer": "^6.4.17", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@types/react-query": "^1.2.9", "autoprefixer": "^10.4.21", "babel-jest": "^30.0.4", "chalk": "^5.4.1", "commander": "^14.0.0", "csv-writer": "^1.6.0", "eslint": "^9.30.1", "eslint-config-next": "15.3.5", "jest": "^30.0.4", "jest-environment-jsdom": "^30.0.4", "jsdom": "^26.1.0", "postcss": "^8.5.6", "supabase": "^2.30.4", "tailwindcss": "^4.1.11", "ts-jest": "^29.4.0", "ts-node": "^10.9.2", "typescript": "^5.8.3"}, "overrides": {"@vercel/node": {"path-to-regexp": "6.3.0"}, "@vercel/remix-builder": {"path-to-regexp": "6.3.0"}}}