# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# Dependencies
/node_modules
/.pnp
.pnp.*

# Testing
/coverage

# Next.js
/.next/
/out/
.next/cache/
.next/out/

# Production
/build

# Environment variables
.env
.env.local
.env.*.local
.env.example
.env.development
.env.test
.env.production
.env.development.local
.env.test.local
.env.staging
src/env.mjs


# Debug logs
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*
debug*.md

# Build output
.vercel/

# TypeScript
*.tsbuildinfo
next-env.d.ts

# Supabase
/supabase/backups/

# Documentation


# Tests - Keeping test files in version control
# /tests/

# Development
api_test.sh
api_test_output.txt
start-server.sh

# Editor directories and files
.vscode/*
!.vscode/extensions.json
!.vscode/settings.json
!.vscode/launch.json
.idea/
.history/
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Local development
.local/
.logs
*.log
docs/security_report.md
chanagelog_enhanced.txt
jest.setup.js
jest.setup.js
token.txt
jest.setup.js

# Sentry Config File
.env.sentry-build-plugin
