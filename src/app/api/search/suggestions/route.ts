/**
 * Refactored Search Suggestions API Route - Phase 2B Step 1
 * 
 * This route has been refactored to use the shared server-side data layer
 * for improved security, performance, and maintainability.
 * 
 * Key improvements:
 * - Uses shared data layer functions instead of direct Supabase queries
 * - Eliminates public key usage in favor of secure server-side access
 * - Consistent error handling and response formats
 * - Better caching strategy
 * - Enhanced functionality with categories, brands, and products
 * - Request validation and rate limiting
 */

import { NextRequest, NextResponse } from 'next/server'
import { getSearchSuggestions } from '@/lib/data'
import { createServerSupabaseReadOnlyClient } from '@/lib/supabase/server'
import { applyRateLimit, rateLimits } from '@/lib/rateLimiter'
import { validateSearchQuery } from '@/lib/utils'
import type { SupabaseClient } from '@supabase/supabase-js'
import { searchSuggestionsSchema, validateInput, createValidationErrorResponse } from '@/lib/validation/schemas';

/**
 * Enhanced response format with routing data
 */
interface SearchSuggestionsResponse {
  categories: Array<{ name: string; slug: string; id: string }>
  brands: Array<{ name: string; slug: string; id: string }>
  products: Array<{ name: string; slug: string; id: string }>
  error?: string
}

/**
 * Enhanced search suggestions with categories, brands, and products
 */
async function getEnhancedSearchSuggestions(supabase: SupabaseClient, query: string, limit = 3): Promise<SearchSuggestionsResponse> {
  try {
    const searchTerm = query.trim()

    if (searchTerm.length < 2) {
      return {
        categories: [],
        brands: [],
        products: [],
      }
    }

    // Get suggestions from all three sources in parallel
    const [categoriesResult, brandsResult, productsResult] = await Promise.all([
      // Categories
      supabase
        .from('categories')
        .select('name, slug, id')
        .ilike('name', `%${searchTerm}%`)
        .limit(limit),
      
      // Brands
      supabase
        .from('brands')
        .select('name, slug, id')
        .ilike('name', `%${searchTerm}%`)
        .limit(limit),
      
      // Products
      supabase
        .from('products')
        .select('name, slug, id')
        .ilike('name', `%${searchTerm}%`)
        .eq('status', 'active')
        .limit(limit),
    ])

    return {
      categories: categoriesResult.data || [],
      brands: brandsResult.data || [],
      products: productsResult.data || [],
    }
  } catch (error) {
    console.error('Error fetching enhanced search suggestions:', error)
    throw error
  }
}

/**
 * GET /api/search/suggestions
 * 
 * Fetches search suggestions for categories, brands, and products
 * 
 * Query Parameters:
 * - q: Search query string (required, minimum 2 characters)
 * - limit: Number of suggestions per category (default: 3, max: 10)
 * 
 * Returns:
 * - categories: Array of matching category names
 * - brands: Array of matching brand names
 * - products: Array of matching product names
 */
export async function GET(request: NextRequest): Promise<NextResponse<SearchSuggestionsResponse>> {
  const startTime = Date.now()

  // Apply rate limiting
  const rateLimitResponse = applyRateLimit(request, rateLimits.search)
  if (rateLimitResponse) {
    return rateLimitResponse as NextResponse<SearchSuggestionsResponse>
  }

  try {
    // Parse and validate query parameters
    const { searchParams } = new URL(request.url)
    const query = searchParams.get('q')
    const limit = Math.min(10, Math.max(1, parseInt(searchParams.get('limit') || '3')))

    // Validate required query parameter
    if (!query) {
      return NextResponse.json(
        {
          categories: [],
          brands: [],
          products: [],
          error: 'Query parameter "q" is required'
        },
        { status: 400 }
      )
    }

    // Validate and sanitize search query
    const searchValidation = validateSearchQuery(query)
    if (!searchValidation.isValid) {
      return NextResponse.json(
        {
          categories: [],
          brands: [],
          products: [],
          error: 'Invalid search query. Please remove any suspicious characters.'
        },
        { status: 400 }
      )
    }

    const sanitizedQuery = searchValidation.sanitized

    // Validate minimum query length after sanitization
    if (sanitizedQuery.length < 2) {
      return NextResponse.json({
        categories: [],
        brands: [],
        products: [],
      })
    }

    const supabase = createServerSupabaseReadOnlyClient();
    // Get enhanced search suggestions using shared data layer with sanitized query
    const suggestions = await getEnhancedSearchSuggestions(supabase, sanitizedQuery, limit)

    // Create Next.js response with proper caching headers
    const nextResponse = NextResponse.json(suggestions)

    // Set cache headers for search suggestions
    // Suggestions can be cached for a moderate time since they don't change frequently
    nextResponse.headers.set(
      'Cache-Control',
      'public, s-maxage=600, stale-while-revalidate=120'
    )

    // Add CORS headers for API access
    nextResponse.headers.set('Access-Control-Allow-Origin', '*')
    nextResponse.headers.set('Access-Control-Allow-Methods', 'GET, OPTIONS')
    nextResponse.headers.set('Access-Control-Allow-Headers', 'Content-Type')

    // Add performance timing header for monitoring
    nextResponse.headers.set('X-Response-Time', `${Date.now() - startTime}ms`)

    return nextResponse

  } catch (error) {
    console.error('Error in search suggestions API route:', error)

    // Return standardized error response in legacy format
    const errorResponse: SearchSuggestionsResponse = {
      categories: [],
      brands: [],
      products: [],
      error: 'Failed to fetch suggestions',
    }

    return NextResponse.json(errorResponse, { status: 500 })
  }
}

/**
 * OPTIONS handler for CORS preflight requests
 */
export async function OPTIONS(): Promise<NextResponse> {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type',
    },
  })
}

/**
 * Runtime configuration
 * Using Node.js runtime for server-side data layer compatibility
 */
export const runtime = 'nodejs'

/**
 * Route segment config for caching
 * Suggestions can be cached longer since they don't change frequently
 */
export const revalidate = 600 // Revalidate every 10 minutes
