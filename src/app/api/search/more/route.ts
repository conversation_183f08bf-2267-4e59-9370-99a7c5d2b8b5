
// src/app/api/search/more/route.ts
// This file defines the API endpoint for fetching additional pages of search results
// for the "Load More" functionality on the search page.

import { NextResponse } from 'next/server';
import { searchProducts } from '@/lib/data/products';
import { TransformedProduct } from '@/lib/data/types';
import { logger } from '@/lib/utils/logger';
import { withTimeout, TIMEOUT_CONFIG } from '@/lib/timeoutConfig';
import { getCacheMetrics } from '@/lib/cache/searchCache';
import { createServerSupabaseReadOnlyClient } from '@/lib/supabase/server';

// Default number of items per page
const DEFAULT_PAGE_SIZE = 20;

/**
 * Handles GET requests to fetch a specific page of search results.
 * This endpoint is designed to be called from the client-side to dynamically
 * load more products without a full page refresh.
 */
export async function GET(request: Request) {
  const requestStartTime = Date.now();
  const requestId = Math.random().toString(36).substring(2, 10);
  
  try {
    // Extract search parameters from the request URL
    const { searchParams, pathname } = new URL(request.url);
    const query = searchParams.get('q') || '';
    const page = Math.max(1, parseInt(searchParams.get('page') || '1', 10));
    const category = searchParams.get('category') || '';
    const subcategory = searchParams.get('subcategory') || '';
    const pageSize = Math.min(50, parseInt(searchParams.get('pageSize') || DEFAULT_PAGE_SIZE.toString(), 10));
    
    logger.info('API Request Received', {
      requestId,
      endpoint: pathname,
      method: 'GET',
      query: query ? '[REDACTED]' : '', // Redact full query in logs
      page,
      pageSize,
      category,
      subcategory,
      hasQuery: !!query,
      hasCategory: !!category,
      hasSubcategory: !!subcategory,
      ip: request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip')
    });

    // Validate input parameters
    if (!query && !category && !subcategory) {
      logger.warn('Empty search parameters', { requestId });
      return NextResponse.json({ 
        products: [],
        totalCount: 0,
        hasMore: false,
        currentPage: 1,
        pageSize: DEFAULT_PAGE_SIZE
      });
    }

    const searchStartTime = Date.now();

    const supabase = createServerSupabaseReadOnlyClient();
    // Wrap search operation with timeout
    const searchTimeout = TIMEOUT_CONFIG.SEARCH.COMPLEX;
    const { products, totalCount } = await withTimeout(
      searchProducts(supabase, query, page, pageSize),
      searchTimeout,
      `Search operation timed out after ${searchTimeout}ms for query: ${query}`
    );

    const searchDuration = Date.now() - searchStartTime;
    
    // Calculate pagination metadata
    const hasMore = (page * pageSize) < totalCount;
    const totalPages = Math.ceil(totalCount / pageSize);

    // Create response
    const response = {
      products,
      currentPage: page,
      pageSize,
      totalCount,
      totalPages,
      hasMore
    };

    // Get cache metrics for monitoring
    const cacheMetrics = getCacheMetrics();

    // Log successful response summary with cache performance
    logger.info('Search completed', {
      requestId,
      productsCount: products.length,
      currentPage: page,
      pageSize,
      totalCount,
      totalPages,
      hasMore,
      searchDurationMs: searchDuration,
      executionTime: Date.now() - requestStartTime,
      cacheHitRate: cacheMetrics.stats.hitRate,
      cacheSize: cacheMetrics.stats.cacheSize
    });

    // Add performance headers
    const responseHeaders = {
      'X-Request-ID': requestId,
      'X-Response-Time': `${Date.now() - requestStartTime}ms`,
      'X-Search-Duration': `${searchDuration}ms`,
      'X-Cache-Hit-Rate': `${cacheMetrics.stats.hitRate}%`,
      'X-Cache-Size': `${cacheMetrics.stats.cacheSize}`,
      'Cache-Control': 'public, s-maxage=300, stale-while-revalidate=600' // 5 min cache, 10 min stale
    };

    return NextResponse.json(response, { headers: responseHeaders });

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    const errorForLogging = error instanceof Error ? error : new Error(String(error));
    
    logger.error('Error in search API', errorForLogging, {
      requestId,
      executionTime: Date.now() - requestStartTime
    });
    
    const responseTime = Date.now() - requestStartTime;
    return NextResponse.json(
      { 
        error: 'Internal Server Error',
        message: 'An error occurred while processing your request',
        requestId
      },
      { 
        status: 500,
        headers: { 
          'Content-Type': 'application/json',
          'X-Request-ID': requestId,
          'X-Response-Time': `${responseTime}ms`
        }
      }
    );
  }
}
