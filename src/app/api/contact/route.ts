/**
 * Refactored Contact API Route - Phase 2B Step 2
 * 
 * This route has been refactored for improved security, performance, and maintainability.
 * 
 * Key improvements:
 * - Enhanced request validation and sanitization
 * - Consistent error handling and response formats
 * - Rate limiting for spam protection
 * - Better email template and error handling
 * - Security headers and CORS support
 * - Comprehensive logging for monitoring
 */

import { NextRequest, NextResponse } from 'next/server'
import nodemailer from 'nodemailer'
import { applyRateLimit, rateLimits } from '@/lib/rateLimiter'
import { contactFormSchema, validateInput, createValidationErrorResponse } from '@/lib/validation/schemas'
import { verifyRequestJWT, createJWT, setJWTCookie, createUnauthorizedResponse } from '@/lib/security/jwt'

/**
 * Contact form data interface
 */
interface ContactFormData {
  name: string
  email: string
  phone?: string
  enquiryType: string
  message: string
}

/**
 * Response interface
 */
interface ContactResponse {
  success?: boolean
  error?: string
  message?: string
}

/**
 * Email configuration
 */
const EMAIL_CONFIG = {
  recipient: '<EMAIL>',
  subject: 'MAIL from CASHBACK DEALS',
  from: process.env.EMAIL_FROM || '"Cashback Deals" <<EMAIL>>',
}

/**
 * Create email transporter with proper configuration
 */
function createEmailTransporter() {
  return nodemailer.createTransport({
    host: process.env.EMAIL_SERVER || 'smtp.gmail.com',
    port: parseInt(process.env.EMAIL_PORT || '587'),
    secure: process.env.EMAIL_SECURE === 'true',
    auth: {
      user: process.env.EMAIL_USER,
      pass: process.env.EMAIL_PASSWORD,
    },
  })
}



/**
 * Sanitize form data to prevent XSS
 */
function sanitizeFormData(data: any): ContactFormData {
  return {
    name: data.name?.toString().trim().substring(0, 100) || '',
    email: data.email?.toString().trim().toLowerCase().substring(0, 255) || '',
    phone: data.phone?.toString().trim().substring(0, 20) || undefined,
    enquiryType: data.enquiryType?.toString().trim().substring(0, 50) || '',
    message: data.message?.toString().trim().substring(0, 5000) || '',
  }
}

/**
 * Generate HTML email template
 */
function generateEmailTemplate(data: ContactFormData): string {
  return `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
      <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 30px; border-radius: 10px 10px 0 0; text-align: center;">
        <h1 style="color: white; margin: 0; font-size: 24px;">New Contact Form Submission</h1>
        <p style="color: #f0f0f0; margin: 10px 0 0 0;">Cashback Deals Website</p>
      </div>
      
      <div style="background: #ffffff; padding: 30px; border: 1px solid #e0e0e0; border-top: none;">
        <div style="margin-bottom: 20px;">
          <h3 style="color: #333; margin: 0 0 10px 0; font-size: 16px;">Contact Information</h3>
          <table style="width: 100%; border-collapse: collapse;">
            <tr>
              <td style="padding: 8px 0; font-weight: bold; color: #555; width: 120px;">Name:</td>
              <td style="padding: 8px 0; color: #333;">${data.name}</td>
            </tr>
            <tr>
              <td style="padding: 8px 0; font-weight: bold; color: #555;">Email:</td>
              <td style="padding: 8px 0; color: #333;"><a href="mailto:${data.email}" style="color: #667eea; text-decoration: none;">${data.email}</a></td>
            </tr>
            ${data.phone ? `
            <tr>
              <td style="padding: 8px 0; font-weight: bold; color: #555;">Phone:</td>
              <td style="padding: 8px 0; color: #333;">${data.phone}</td>
            </tr>
            ` : ''}
            <tr>
              <td style="padding: 8px 0; font-weight: bold; color: #555;">Enquiry Type:</td>
              <td style="padding: 8px 0; color: #333;">${data.enquiryType}</td>
            </tr>
          </table>
        </div>
        
        <div>
          <h3 style="color: #333; margin: 0 0 15px 0; font-size: 16px;">Message</h3>
          <div style="background: #f8f9fa; padding: 20px; border-radius: 5px; border-left: 4px solid #667eea;">
            <p style="margin: 0; line-height: 1.6; color: #333; white-space: pre-wrap;">${data.message}</p>
          </div>
        </div>
      </div>
      
      <div style="background: #f8f9fa; padding: 20px; border-radius: 0 0 10px 10px; text-align: center; border: 1px solid #e0e0e0; border-top: none;">
        <p style="margin: 0; color: #666; font-size: 12px;">
          This email was sent from the contact form on Cashback Deals website.<br>
          Sent on ${new Date().toLocaleString()}
        </p>
      </div>
    </div>
  `
}

/**
 * Verify Cloudflare Turnstile token
 *
 * @param token - The Turnstile token from the client
 * @param ip - The client IP address
 * @returns Promise<boolean> - True if verification succeeds
 */
async function verifyTurnstileToken(token: string, ip: string): Promise<boolean> {
  try {
    // Check if we have the secret key
    const secretKey = process.env.TURNSTILE_SECRET_KEY;
    if (!secretKey) {
      console.error('TURNSTILE_SECRET_KEY environment variable is not set');
      return false;
    }

    // Prepare form data for Cloudflare verification
    const formData = new FormData();
    formData.append('secret', secretKey);
    formData.append('response', token);
    formData.append('remoteip', ip);

    // Call Cloudflare's siteverify endpoint
    const verificationResponse = await fetch('https://challenges.cloudflare.com/turnstile/v0/siteverify', {
      method: 'POST',
      body: formData,
    });

    if (!verificationResponse.ok) {
      console.error('Turnstile verification request failed:', verificationResponse.status);
      return false;
    }

    const result = await verificationResponse.json();

    // Log verification result for debugging (without exposing sensitive data)
    console.log('Turnstile verification result:', {
      success: result.success,
      challenge_ts: result.challenge_ts,
      hostname: result.hostname,
      error_codes: result['error-codes']
    });

    return result.success === true;
  } catch (error) {
    console.error('Error verifying Turnstile token:', error);
    return false;
  }
}

/**
 * POST /api/contact
 *
 * Two-step process:
 * 1. If Turnstile token present: verify CAPTCHA and issue JWT
 * 2. If JWT present: process form submission
 */
export async function POST(request: NextRequest): Promise<NextResponse<ContactResponse>> {
  const startTime = Date.now()

  // Apply rate limiting for spam protection
  const rateLimitResponse = applyRateLimit(request, rateLimits.contact)
  if (rateLimitResponse) {
    return rateLimitResponse as NextResponse<ContactResponse>
  }

  try {
    // Parse request body
    const rawData = await request.json()
    console.log('Received contact form data:', rawData);

    // Check if this is a Turnstile verification request (step 1)
    const turnstileToken = rawData['cf-turnstile-response'];
    if (turnstileToken) {
      return await handleTurnstileVerification(request, turnstileToken, rawData)
    }

    // Otherwise, this should be a form submission with JWT (step 2)
    return await handleFormSubmission(request, rawData, startTime)

  } catch (error) {
    console.error('Error processing contact form:', error)

    return NextResponse.json(
      {
        error: 'Internal server error',
        message: 'Please try again later',
      },
      { status: 500 }
    )
  }
}

/**
 * Step 1: Handle Turnstile verification and JWT issuance
 */
async function handleTurnstileVerification(
  request: NextRequest, 
  turnstileToken: string, 
  rawData: any
): Promise<NextResponse<ContactResponse>> {
  console.log('Processing Turnstile verification');

  // Get client IP for Turnstile verification
  const forwarded = request.headers.get('x-forwarded-for');
  const ip = forwarded ? forwarded.split(',')[0].trim() : '127.0.0.1';

  // Verify the Turnstile token
  const isTurnstileValid = await verifyTurnstileToken(turnstileToken, ip);
  if (!isTurnstileValid) {
    return NextResponse.json(
      {
        error: 'CAPTCHA verification failed',
        message: 'Invalid CAPTCHA token. Please try again.',
      },
      { status: 400 }
    );
  }

  // Create JWT token
  try {
    const jwt = await createJWT()
    
    const response = NextResponse.json({
      success: true,
      message: 'CAPTCHA verified. You may now submit your form.',
      token: jwt // Also return in response for header-based clients
    })

    // Set JWT as HttpOnly cookie
    setJWTCookie(response, jwt)

    // Add security headers
    response.headers.set('X-Content-Type-Options', 'nosniff')
    response.headers.set('X-Frame-Options', 'DENY')
    response.headers.set('X-XSS-Protection', '1; mode=block')

    console.log('JWT issued after successful Turnstile verification')
    return response
    
  } catch (error) {
    console.error('Failed to create JWT:', error)
    return NextResponse.json(
      {
        error: 'Authentication system error',
        message: 'Please try again later',
      },
      { status: 500 }
    )
  }
}

/**
 * Step 2: Handle form submission with JWT verification
 */
async function handleFormSubmission(
  request: NextRequest,
  rawData: any,
  startTime: number
): Promise<NextResponse<ContactResponse>> {
  console.log('Processing form submission with JWT verification');

  // Verify JWT token (from header or cookie)
  const jwtPayload = await verifyRequestJWT(request)
  if (!jwtPayload) {
    console.log('Missing or invalid JWT for form submission')
    return NextResponse.json(
      { 
        error: 'Unauthorized',
        message: 'Valid authentication token required for form submission'
      },
      { status: 401 }
    )
  }

  console.log('JWT verified successfully for form submission');

  // Validate form data with Zod schema
  const validation = validateInput(contactFormSchema, rawData)
  if (!validation.success) {
    console.log('Validation errors:', validation.error, validation.details);
    return NextResponse.json(
      createValidationErrorResponse(validation.error, validation.details),
      { status: 400 }
    )
  }

  // Use validated and type-safe data
  const formData = validation.data

  // Check email configuration
  if (!process.env.EMAIL_USER || !process.env.EMAIL_PASSWORD) {
    console.error('Email configuration missing')
    return NextResponse.json(
      {
        error: 'Email service temporarily unavailable',
        message: 'Please try again later or contact us directly',
      },
      { status: 503 }
    )
  }

  // Create email transporter
  const transporter = createEmailTransporter()

  // Test email connection
  try {
    console.log('Testing email connection...')
    await transporter.verify()
    console.log('Email connection verified successfully')
  } catch (verifyError: any) {
    console.error('Email connection verification failed:', verifyError)
    return NextResponse.json(
      {
        error: 'Email service temporarily unavailable',
        message: 'Please try again later or contact us directly',
      },
      { status: 503 }
    )
  }

  // Generate email content
  const htmlContent = generateEmailTemplate(formData)

  // Send email
  try {
    console.log('Attempting to send email with config:', {
      from: EMAIL_CONFIG.from,
      to: EMAIL_CONFIG.recipient,
      subject: `${EMAIL_CONFIG.subject} - ${formData.enquiryType}`,
      host: process.env.EMAIL_SERVER,
      port: process.env.EMAIL_PORT,
      user: process.env.EMAIL_USER,
      // Don't log password for security
    })

    const result = await transporter.sendMail({
      from: EMAIL_CONFIG.from,
      to: EMAIL_CONFIG.recipient,
      subject: `${EMAIL_CONFIG.subject} - ${formData.enquiryType}`,
      html: htmlContent,
      replyTo: formData.email,
      headers: {
        'X-Contact-Form': 'Cashback Deals',
        'X-Enquiry-Type': formData.enquiryType,
      },
    })

    console.log('Contact form email sent successfully:', {
      messageId: result.messageId,
      response: result.response,
      accepted: result.accepted,
      rejected: result.rejected
    })
  } catch (emailError: any) {
    console.error('Error sending contact form email:', emailError)
    console.error('Email error details:', {
      message: emailError?.message,
      code: emailError?.code,
      response: emailError?.response,
      responseCode: emailError?.responseCode
    })
    
    return NextResponse.json(
      {
        error: 'Failed to send message',
        message: 'Please try again later or contact us directly',
      },
      { status: 500 }
    )
  }

  // Create success response
  const response: ContactResponse = {
    success: true,
    message: 'Thank you for your message. We will get back to you soon!',
  }

  const nextResponse = NextResponse.json(response)

  // Add security headers
  nextResponse.headers.set('X-Content-Type-Options', 'nosniff')
  nextResponse.headers.set('X-Frame-Options', 'DENY')
  nextResponse.headers.set('X-XSS-Protection', '1; mode=block')

  // Add CORS headers 
  // TODO(PR5): Tighten CORS origins to specific domains (https://cashback-deals.com, https://*.amplifyapp.com)
  nextResponse.headers.set('Access-Control-Allow-Origin', '*')
  nextResponse.headers.set('Access-Control-Allow-Methods', 'POST, OPTIONS')
  nextResponse.headers.set('Access-Control-Allow-Headers', 'Content-Type, Authorization')

  // Add performance timing header
  nextResponse.headers.set('X-Response-Time', `${Date.now() - startTime}ms`)

  return nextResponse
}

/**
 * OPTIONS handler for CORS preflight requests
 */
export async function OPTIONS(): Promise<NextResponse> {
  return new NextResponse(null, {
    status: 200,
    headers: {
      // TODO(PR5): Tighten CORS origins to specific domains
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  })
}

/**
 * Runtime configuration
 * Using Node.js runtime for email functionality
 */
export const runtime = 'nodejs'
